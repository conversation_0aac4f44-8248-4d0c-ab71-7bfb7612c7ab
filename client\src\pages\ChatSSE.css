.chat-container {
  width: 800px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.chat-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 20px;
}

.chat-messages {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  background-color: #f9f9f9;
  margin-bottom: 20px;
}

.message {
  margin-bottom: 15px;
  padding: 10px 15px;
  border-radius: 18px;
  max-width: 80%;
  word-wrap: break-word;
}

.user-message {
  background-color: #dcf8c6;
  margin-left: auto;
  margin-right: 10px;
  text-align: right;
  border-bottom-right-radius: 5px;
}

.ai-message {
  background-color: #f1f0f0;
  margin-left: 10px;
  border-bottom-left-radius: 5px;
}

.ai-message.typing::after {
  content: '▌';
  animation: cursor-blink 1s step-start infinite;
}

@keyframes cursor-blink {
  50% { opacity: 0; }
}

.pre-text {
  white-space: pre-wrap;
  margin: 0;
  font-family: inherit;
}

.chat-input-container {
  display: flex;
  gap: 10px;
}

.chat-textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: inherit;
  resize: none;
  height: 60px;
}

.chat-button {
  padding: 0 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.chat-button:hover {
  background-color: #45a049;
}

.chat-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.chat-status {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}
