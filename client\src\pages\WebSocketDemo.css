.ws-container {
  width: 800px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.ws-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 20px;
}

.ws-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.ws-connection-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.ws-typing-area {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
  background-color: #f9f9f9;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.ws-typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #333;
  margin-left: 2px;
  animation: cursor-blink 1s step-start infinite;
}

@keyframes cursor-blink {
  50% { opacity: 0; }
}

.ws-input-container {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.ws-examples {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.ws-textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: inherit;
  resize: vertical;
  min-height: 60px;
}

.ws-button {
  padding: 10px 15px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.ws-button:hover {
  background-color: #45a049;
}

.ws-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.ws-status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.ws-status-indicator.connected {
  background-color: #4caf50;
}

.ws-status-indicator.connecting {
  background-color: #ff9800;
}

.ws-status-indicator.disconnected {
  background-color: #f44336;
}

.ws-connection-status {
  display: flex;
  align-items: center;
}

.ws-heartbeat {
  font-size: 0.8em;
  color: #666;
}

.ws-explanation {
  margin-top: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.ws-explanation h3 {
  margin-top: 0;
}
