<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话打字效果演示</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
            margin-bottom: 20px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user-message {
            background-color: #dcf8c6;
            margin-left: auto;
            margin-right: 10px;
            text-align: right;
            border-bottom-right-radius: 5px;
        }
        .ai-message {
            background-color: #f1f0f0;
            margin-left: 10px;
            border-bottom-left-radius: 5px;
        }
        .ai-message.typing::after {
            content: '▌';
            animation: cursor-blink 1s step-start infinite;
        }
        @keyframes cursor-blink {
            50% { opacity: 0; }
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        textarea {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
            resize: none;
            height: 60px;
        }
        button {
            padding: 0 20px;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        pre {
            white-space: pre-wrap;
            margin: 0;
            font-family: inherit;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI对话打字效果演示</h1>
        
        <div class="chat-container" id="chatContainer">
            <!-- 消息将在这里动态添加 -->
        </div>
        
        <div class="input-container">
            <textarea id="userInput" placeholder="请输入您的问题..."></textarea>
            <button id="sendButton">发送</button>
        </div>
        
        <div class="status" id="status"></div>
    </div>

    <script src="js/chat.js"></script>
</body>
</html>
